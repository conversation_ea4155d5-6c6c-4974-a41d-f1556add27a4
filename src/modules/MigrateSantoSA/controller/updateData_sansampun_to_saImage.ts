import { connectdocker } from "help/connectDataBase";
import { DatabaseConfig } from "help/database.enum";
import { responseError, responseSuccess } from "help/response";
import { getPhoneDataFromFirestore, parseFirestoreData, updatePhoneInFirestoreMenuMyCar } from "help/sendNotificationFCM";

class MigrateJobController {
  public async MigrateJob(req: any) {
    try {
      let bodyData = await req.data;
  
      console.log(bodyData);
  
      // Get data from AFSInvoiceHistoryReportSummary
      const resSummary = await connectdocker.connect(
        DatabaseConfig.MIRAI,
        `SELECT *
          FROM AFSInvoiceHistoryReportSummary
          WHERE Column24 = ?
          ORDER BY create_time DESC
          LIMIT 1`,
        [bodyData.phone]
      );
  
      // Get data from Firebase
      const resFirebase = await getPhoneDataFromFirestore(bodyData.phone, "myCar/EcnyzdWmCIxBrRnh5QTk");
  
      // Parse Firebase data
      let firebaseCarList = [];
      if (resFirebase && resFirebase.documents && resFirebase.documents.length > 0) {
        for (let i = 0; i < resFirebase.documents.length; i++) {
          const parsedData = parseFirestoreData(resFirebase.documents[i].fields);
          firebaseCarList.push(parsedData);
        }
      }
  
      const updatedData: any[] = [];
  
      // ✅ เช็คความพร้อมของข้อมูลก่อนเริ่มเปรียบเทียบ
      if (!resSummary || resSummary.length === 0) {
        console.log("No data found in resSummary");
        return await responseSuccess({ message: "No summary data" }, 201);
      }
  
      if (!firebaseCarList || firebaseCarList.length === 0) {
        console.log("No data found in firebaseCarList");
        return await responseSuccess({ message: "No firebase data" }, 201);
      }
  
      // ✅ ตรวจสอบว่า summaryDate ใหม่กว่าหรือไม่
      const summaryData = resSummary[0];
      const summaryDateRaw = summaryData?.create_time; // ต้องเป็นฟิลด์วันที่ใน AFS
  
      let isSummaryNewer = true;
  
      if (firebaseCarList.length > 0 && summaryDateRaw) {
        const summaryDate = new Date(summaryDateRaw);
  
        // หาวันล่าสุดจาก Firebase car list
        const firebaseDates = firebaseCarList.map(c => new Date(c.lastUpdated)).filter(d => !isNaN(d.getTime()));
        const latestFirebaseDate = firebaseDates.length > 0 ? new Date(Math.max(...firebaseDates.map(d => d.getTime()))) : null;
  
        if (latestFirebaseDate && summaryDate <= latestFirebaseDate) {
          isSummaryNewer = false;
        }
      }
  
      if (!isSummaryNewer) {
        console.log("Summary data is not newer than Firebase. Skip update.");

        return await responseSuccess({ message: "No new data to update" }, 201);
      }
  
      // ✅ หากข้อมูลใหม่กว่า ทำการเปรียบเทียบและอัปเดต
      const fieldMappings = [
        { firebase: "phone", summary: "Column24" },
        { firebase: "phone", summary: "Column26" },
        { firebase: "reg", summary: "Column27" },
        { firebase: "car_id", summary: "Column28" },
        { firebase: "machine_number", summary: "Column29" },
        { firebase: "miles[0].mileage", summary: "Column35" },
        { firebase: "miles[0].mileage", summary: "Column37" },
        { firebase: "miles[0].mileage_date", summary: "Column47" },
        { firebase: "miles[0].mileage_date", summary: "Column49" },
        { firebase: "miles[0].mileage_date", summary: "Column68" },
        { firebase: "miles[0].mileage_date", summary: "Column70" },
        { firebase: "miles[0].mileage_date", summary: "Column76" },
        { firebase: "miles[0].mileage_date", summary: "Column77" },
        { firebase: "miles[0].mileage_date", summary: "Column112" }
      ];
  
      for (const firebaseCar of firebaseCarList) {
        if (!firebaseCar) continue;
  
        const updatedCar = { ...firebaseCar };
        let hasUpdates = false;
  
        const isMatchingCar =
          firebaseCar.phone === summaryData.Column24 ||
          firebaseCar.phone === summaryData.Column26 ||
          firebaseCar.machine_number === summaryData.Column29;
  
        if (isMatchingCar) {
          console.log(`Found matching car for phone: ${firebaseCar.phone}, machine: ${firebaseCar.machine_number}`);
  
          for (const mapping of fieldMappings) {
            const summaryValue = summaryData[mapping.summary];
  
            if (summaryValue !== null && summaryValue !== undefined && summaryValue !== '') {
              let firebaseValue;
              const path = mapping.firebase;
              const keys = path.split('.');
              let current = firebaseCar;
  
              for (const key of keys) {
                if (key.includes('[') && key.includes(']')) {
                  const arrayKey = key.substring(0, key.indexOf('['));
                  const index = parseInt(key.substring(key.indexOf('[') + 1, key.indexOf(']')));
                  current = current?.[arrayKey]?.[index];
                } else {
                  current = current?.[key];
                }
              }
              firebaseValue = current;
  
              let isNewer = false;
              const firebaseValueStr = (typeof firebaseValue === 'object' && firebaseValue !== null)
                ? JSON.stringify(firebaseValue)
                : String(firebaseValue || '');
  
              if (firebaseValue === null || firebaseValue === undefined || firebaseValueStr === '' || firebaseValueStr === 'null' || firebaseValueStr === 'undefined') {
                isNewer = true;
              } else if (path.includes('date')) {
                try {
                  const summaryDate = new Date(summaryValue);
                  const firebaseDate = new Date(firebaseValueStr);
  
                  if (!isNaN(summaryDate.getTime()) && !isNaN(firebaseDate.getTime())) {
                    isNewer = summaryDate > firebaseDate;
                  } else {
                    isNewer = String(summaryValue) !== firebaseValueStr;
                  }
                } catch (error) {
                  console.log(`Error comparing dates: ${error}`);
                  isNewer = String(summaryValue) !== firebaseValueStr;
                }
              } else {
                isNewer = String(summaryValue) !== firebaseValueStr && summaryValue !== null && summaryValue !== undefined && summaryValue !== '';
              }
  
              if (isNewer) {
                const setKeys = path.split('.');
                let setCurrent = updatedCar;
  
                for (let i = 0; i < setKeys.length - 1; i++) {
                  const key = setKeys[i];
                  if (key.includes('[') && key.includes(']')) {
                    const arrayKey = key.substring(0, key.indexOf('['));
                    const index = parseInt(key.substring(key.indexOf('[') + 1, key.indexOf(']')));
                    if (!setCurrent[arrayKey]) setCurrent[arrayKey] = [];
                    if (!setCurrent[arrayKey][index]) setCurrent[arrayKey][index] = {};
                    setCurrent = setCurrent[arrayKey][index];
                  } else {
                    if (!setCurrent[key]) setCurrent[key] = {};
                    setCurrent = setCurrent[key];
                  }
                }
  
                const lastKey = setKeys[setKeys.length - 1];
                if (lastKey.includes('[') && lastKey.includes(']')) {
                  const arrayKey = lastKey.substring(0, lastKey.indexOf('['));
                  const index = parseInt(lastKey.substring(lastKey.indexOf('[') + 1, lastKey.indexOf(']')));
                  if (!setCurrent[arrayKey]) setCurrent[arrayKey] = [];
                  setCurrent[arrayKey][index] = summaryValue;
                } else {
                  setCurrent[lastKey] = summaryValue;
                }
  
                hasUpdates = true;
                console.log(`Updated ${mapping.firebase} from ${firebaseValue} to ${summaryValue}`);
              }
            }
          }
  
          if (hasUpdates) {
            updatedCar.lastUpdated = new Date().toISOString();
            updatedCar.updateSource = 'AFSInvoiceHistoryReportSummary';
            updatedData.push(updatedCar);
          }
        }
      }
  
      for (let i = 0; i < updatedData.length; i++) {
        const docId = resFirebase.documents[i].name.split('/');
        await updatePhoneInFirestoreMenuMyCar(
          bodyData.phone,
          "myCar/EcnyzdWmCIxBrRnh5QTk",
          docId[8],
          updatedData[i]
        );
      }

      return await responseSuccess({
        augmentedData: updatedData
      }, 200);
    } catch (error) {
      return await responseError(error, 500);
    }
  }
  
}

export let MigrateJobModule = new MigrateJobController();
