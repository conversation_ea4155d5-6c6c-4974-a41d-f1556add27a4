
import {responseError, responseSuccess} from "../../../help/response";
import { connectdocker } from "../../../help/connectDataBase";
import { DatabaseConfig } from "../../../help/database.enum";
class GetAppointment {
    public async getAppointment(req: any, res: any) {
       try{
            console.log("getAllLicensePlateByPhone");
            const params = await req.params;
            console.log(params);
            var  res  = await connectdocker.connect(
                DatabaseConfig.BCT_PMS_Appointment,
                `SELECT service_date_license, need_date, time_date_need, confirmation, status_appointments, product FROM Appointment WHERE service_date_tel = ? AND confirmation = "จองแล้ว" ORDER BY need_date DESC LIMIT 1`,
                [params.phone]
            )
          return responseSuccess(res, 200);
          }catch (e){
            console.log(e);
            return await responseError("ERROR :: Get All License Plate", 407);
          }
    }
}

export let getAppointmentModule = new GetAppointment();

